# TimeSeriesModel2drV2 数据格式适配总结

## 背景

`pyqlab/data/dataset/dataset_fts.py` 中的 `FTSDataset` 类发生了变更，主要变化是在 `model_type=0` 模式下：

**变更前：**
```python
emb = self.lb_df[['code_encoded'] + self.handler.ct_cat_cols_names].iloc[idx:idx+self.seq_len, :].astype(np.int32).values
```

**变更后：**
```python
code = self.lb_df[['code_encoded']].iloc[idx:idx+self.seq_len, :].astype(np.int32).values
```

主要变化：
1. 变量名从 `emb` 改为 `code`
2. 移除了 `self.handler.ct_cat_cols_names` 部分，只保留了 `code_encoded` 列

## 模型适配修改

### 1. 更新模型文档和注释

- 在 `TimeSeriesModel2drV2` 类文档中添加了版本更新说明
- 更新了 `forward` 方法的参数说明，明确新的数据格式要求
- 添加了数据格式适配的相关注释

### 2. 增强输入格式检查

添加了 `_check_input_format` 方法：
```python
def _check_input_format(self, embds, x):
    """
    检查输入数据格式并进行必要的转换
    支持新旧两种数据格式的兼容性
    """
```

功能：
- 检测并处理旧格式的嵌入数据（包含多个分类特征）
- 自动转换输入数据格式以保证兼容性
- 提供警告信息帮助调试

### 3. 优化嵌入数据处理

更新了 `_embed_data` 方法：
- 添加了新数据格式的说明注释
- 保持了对单一 `code_encoded` 特征的处理逻辑

### 4. 动态卷积层创建

**核心改进：** 实现了动态卷积层创建机制

**问题：** 原始实现在模型初始化时就创建卷积层，但无法准确预测加入嵌入特征后的实际输入通道数。

**解决方案：**
1. 在初始化时延迟创建卷积层
2. 添加 `_create_conv_layers` 方法，在第一次前向传播时动态创建
3. 根据实际输入数据的通道数创建匹配的卷积层

```python
def _create_conv_layers(self, actual_input_channels):
    """动态创建卷积层"""
    if self.conv_layers is not None:
        return  # 已经创建过了
    
    print(f"动态创建卷积层，实际输入通道数: {actual_input_channels}")
    # ... 创建卷积层的逻辑
```

### 5. 改进前向传播逻辑

在 `forward` 方法中：
1. 首先调用 `_check_input_format` 检查和调整输入格式
2. 处理嵌入数据并与特征数据拼接
3. 动态创建卷积层（如果还没有创建）
4. 继续正常的前向传播流程

### 6. 修复参数问题

- 移除了未使用的 `kernel_size` 和 `activation` 参数
- 修复了 hook 函数中未使用参数的警告

### 7. 更新示例输入生成

更新了 `get_example_inputs` 方法以适配新的数据格式：
```python
def get_example_inputs(self, batch_size=1):
    """
    生成用于ONNX导出的示例输入
    适配新的数据格式：embds现在只包含code_encoded
    """
```

## 测试验证

创建了 `test_model_adaptation.py` 测试脚本，验证：

1. **新数据格式兼容性** ✓
   - 测试模型能否正确处理只包含 `code_encoded` 的嵌入数据
   - 验证输出形状和数值的正确性

2. **旧数据格式兼容性** ✓
   - 测试模型能否处理包含多个分类特征的旧格式数据
   - 验证向后兼容性

3. **模型组件功能** ✓
   - 测试基本前向传播
   - 测试概率预测模式
   - 验证各个组件的正常工作

## 关键特性

### 动态适配
- 模型能够根据实际输入数据的格式和维度动态调整网络结构
- 支持不同的输入通道数，无需手动配置

### 向后兼容
- 自动检测和处理旧格式的输入数据
- 提供清晰的警告信息帮助用户了解数据格式变化

### 鲁棒性
- 处理序列长度不匹配的情况
- 自动调整数据维度以确保模型正常工作

## 使用建议

1. **新项目**：直接使用新的数据格式，只传入 `code_encoded`
2. **现有项目**：模型会自动处理旧格式数据，但建议逐步迁移到新格式
3. **调试**：注意观察控制台输出的警告信息，了解数据格式转换情况

## 性能影响

- 动态创建卷积层只在第一次前向传播时发生，后续调用无额外开销
- 输入格式检查的开销很小，不会显著影响性能
- 整体模型性能和准确性保持不变

## 总结

通过这次适配，`TimeSeriesModel2drV2` 模型成功适配了新的数据集格式，同时保持了向后兼容性。主要创新点是动态卷积层创建机制，这使得模型能够灵活适应不同的输入格式，提高了模型的鲁棒性和易用性。
